import { selector } from 'recoil';
import { activeProductsAtom } from '@recoil/products/activeProductsAtom';

const productOptionsSelector = selector({
  key: 'productOptionsSelector',
  get: ({ get }) => {
    const products = get(activeProductsAtom);
    const list = products?.map(({ record }) => ({
      key: record.name,
      value: record.id,
    }));
    return list;
  },
});

export default productOptionsSelector;
