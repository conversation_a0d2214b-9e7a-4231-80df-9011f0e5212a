import { useEffect } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { useHasChanged } from '@hooks';
import { selectedEventState } from '@recoil/eventSidebar';
import { availableAgentsSelector } from '@recoil/agents';

const useValidateSelectedEvent = () => {
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);
  const availableAgents = useRecoilValue(availableAgentsSelector);
  const {
    attributes: selectedAttributes,
    oids: selectedOids,
    type: selectedType,
    formFieldErrors,
  } = selectedEvent;

  const attributesChanged = useHasChanged(selectedAttributes);
  const eventTypeChanged = useHasChanged(selectedType);

  useEffect(() => {
    let filteredSelectedOids = selectedOids;

    // Available agents will filter out ineligible agents if
    // attributes or eventtypes change. We need to remove any of these agents
    // if they have already been selected.
    if (attributesChanged || eventTypeChanged) {
      filteredSelectedOids = selectedOids.filter((oid) => {
        return availableAgents.findIndex((agent) => agent.oid === oid) > -1;
      });
    }

    // If some OIDs have been filtered out of the selected event:
    if (filteredSelectedOids.length < selectedOids.length) {
      setSelectedEvent({
        ...selectedEvent,
        // Set the new filtered oids
        oids: filteredSelectedOids,
        // Set the error message for the oids field (without erasing other errors that may exist)
        formFieldErrors: {
          ...formFieldErrors,
          oids: 'Selected truck was not permitted to perform this job.',
        },
      });
    }
  }, [
    attributesChanged,
    eventTypeChanged,
    selectedOids,
    availableAgents,
    selectedEvent,
    formFieldErrors,
    setSelectedEvent,
  ]);
};

export default useValidateSelectedEvent;
