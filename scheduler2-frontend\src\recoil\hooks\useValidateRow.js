import { useRecoilValue } from 'recoil';
import { draggingEventAtom } from '@recoil/event';

const useValidateRow = (row, restrictDndByEventType) => {
  const draggingEvent = useRecoilValue(draggingEventAtom);

  // If no event being dragged, nothing changes
  if (!draggingEvent) return false;

  const { eventTypes, attributes } = row;

  // Check if agent can perform the event type
  const canPerformEventType = restrictDndByEventType
    ? eventTypes.includes(draggingEvent?.type)
    : true;

  // Check if agent can perform all attributes on the dragged event
  const canPerformAttributes = draggingEvent?.attributes.every((attribute) =>
    attributes.includes(attribute),
  );

  return !canPerformAttributes || !canPerformEventType;
};

export default useValidateRow;
