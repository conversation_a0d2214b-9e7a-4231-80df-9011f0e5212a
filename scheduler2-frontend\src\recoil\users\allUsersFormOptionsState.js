import { selector } from 'recoil';
import { decodeEventType } from '@homeworksenergy/utility-service';
import { calendarTypeAtom } from '@recoil/app';
import { usersSelectorFamily } from '@recoil/users';

const allUsersFormOptionsState = selector({
  key: 'allUsersFormOptions',
  get: ({ get }) => {
    const calendarType = get(calendarTypeAtom);
    // TODO: There a is bug where calendarType is coming in as null which makes decodeEventTypes return an error. Defaulting a value resolves the issue.
    const { business: department } = decodeEventType(calendarType || '000000');

    // Get all crews for the business type
    const allAgents = get(usersSelectorFamily(department));

    return allAgents.map(({ displayName, oid, region }) => {
      return { key: displayName, value: oid, region };
    });
  },
});

export default allUsersFormOptionsState;
