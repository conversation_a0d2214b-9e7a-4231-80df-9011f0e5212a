import { selectorFamily } from 'recoil';
import { UsersManager } from '@utils/APIManager';
import { refreshAdminListState } from '@recoil/admin';

// TODO: does this need to be a selector family? We don't currently pass anything to the getUsersInfo function
const usersSelectorFamily = selectorFamily({
  key: 'usersSelectorFamily',
  get: () => async ({ get }) => {
    // counter used to force async refresh selector
    get(refreshAdminListState);

    const allUsers = await UsersManager.getUsersInfo();

    return allUsers;
  },
});

export default usersSelectorFamily;
