import { atom, selector, DefaultValue } from 'recoil';
import { capWorkReceiptFields } from '@pages/WorkReceipt/CAPWR/FormSchema/fieldsMap';
import { getDataIntakeFormValues } from '@components/DataIntakeForm/dataIntakeFormHelpers';

const defaultValues = getDataIntakeFormValues(capWorkReceiptFields);
const numberOfIndices = 4;
const capWorkReceiptValuesAtom = [];

Array.from({ length: numberOfIndices }, (_, index) => {
  const atoms = {};
  Object.entries(defaultValues).forEach(([fieldName, defaultValue]) => {
    atoms[fieldName] = atom({
      key: `capWorkReceiptValuesAtom-${index}-${fieldName}`,
      default: defaultValue,
    });
  });
  return capWorkReceiptValuesAtom.push(atoms);
});

const capWorkReceiptValuesState = selector({
  key: 'capWorkReceiptValuesSelector',
  get: ({ get }) => {
    const response = capWorkReceiptValuesAtom.map((item) => {
      const fields = {};
      const propertyNames = Object.keys(item);

      propertyNames.forEach((propertyName) => {
        fields[propertyName] = get(item[propertyName]);
      });

      return fields;
    });

    return response;
  },
  set: ({ set, reset }, newValues) => {
    const { index, ...rest } = newValues;

    if (newValues instanceof DefaultValue) {
      capWorkReceiptValuesAtom.forEach((item) => {
        const propertyNames = Object.keys(item);

        propertyNames.forEach((propertyName) => {
          reset(item[propertyName]);
        });
      });

      return;
    }

    if (Array.isArray(newValues)) {
      if (capWorkReceiptValuesAtom.length > newValues.length) {
        capWorkReceiptValuesAtom.splice(newValues.length);
      }
      newValues.forEach((item, index) => {
        Object.entries(item).forEach(([fieldName, newValue]) => {
          if (capWorkReceiptValuesAtom[index]?.[fieldName]) {
            set(capWorkReceiptValuesAtom[index][fieldName], newValue);
          }
        });
      });
    } else {
      const currentAtoms = capWorkReceiptValuesAtom[index];
      Object.entries(rest).forEach(([fieldName, newValue]) => {
        if (currentAtoms?.[fieldName]) {
          set(currentAtoms[fieldName], newValue);
        }
      });
    }
  },
});

export default capWorkReceiptValuesState;
